%%
%% This is file `sample-manuscript.tex',
%% generated with the docstrip utility.
%%
%% The original source files were:
%%
%% samples.dtx  (with options: `all,proceedings,bibtex,manuscript')
%% 
%% IMPORTANT NOTICE:
%% 
%% For the copyright see the source file.
%% 
%% Any modified versions of this file must be renamed
%% with new filenames distinct from sample-manuscript.tex.
%% 
%% For distribution of the original source see the terms
%% for copying and modification in the file samples.dtx.
%% 
%% This generated file may be distributed as long as the
%% original source files, as listed above, are part of the
%% same distribution. (The sources need not necessarily be
%% in the same archive or directory.)
%% 
%% 
%% Commands for TeXCount
%TC:macro \cite [option:text,text]
%TC:macro \citep [option:text,text]
%TC:macro \citet [option:text,text]
%TC:envir table 0 1
%TC:envir table* 0 1
%TC:envir tabular [ignore] word
%TC:envir displaymath 0 word
%TC:envir math 0 word
%TC:envir comment 0 0
%%
%% The first command in your LaTeX source must be the \documentclass
%% command.
%%
%% For submission and review of your manuscript please change the
%% command to \documentclass[manuscript, screen, review]{acmart}.
%%
%% When submitting camera ready or to TAPS, please change the command
%% to \documentclass[sigconf]{acmart} or whichever template is required
%% for your publication.
%%
%%
\documentclass[manuscript,screen,review]{acmart}

\usepackage{amsmath,amsfonts}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{xcolor}


% \usepackage{subfigure}
\usepackage{multirow}
\usepackage{booktabs} 
\usepackage{caption}
\usepackage{subcaption}
\usepackage{tabularx}
\usepackage{makecell}
\usepackage{appendix}

%%
%% \BibTeX command to typeset BibTeX logo in the docs
\AtBeginDocument{%
  \providecommand\BibTeX{{%
    Bib\TeX}}}

%% Rights management information.  This information is sent to you
%% when you complete the rights form.  These commands have SAMPLE
%% values in them; it is your responsibility as an author to replace
%% the commands and values with those provided to you when you
%% complete the rights form.
\setcopyright{acmlicensed}
\copyrightyear{2025}
\acmYear{2025}
\acmDOI{XXXXXXX.XXXXXXX}
%% These commands are for a PROCEEDINGS abstract or paper.
\acmConference[Conference acronym 'XX]{Make sure to enter the correct conference title from your rights confirmation email}{June 03--05, 2018}{Woodstock, NY}
% \acmJournal[Conference acronym 'XX]{Make sure to enter the correct conference title from your rights confirmation email}{June 03--05, 2018}{Woodstock, NY}
%%
%%  Uncomment \acmBooktitle if the title of the proceedings is different
%%  from ``Proceedings of ...''!
%%
%%\acmBooktitle{Woodstock '18: ACM Symposium on Neural Gaze Detection,
%%  June 03--05, 2018, Woodstock, NY}
\acmISBN{978-1-4503-XXXX-X/2018/06}


%%
%% Submission ID.
%% Use this when submitting an article to a sponsored event. You'll
%% receive a unique submission ID from the organizers
%% of the event, and this ID should be used as the parameter to this command.
%%\acmSubmissionID{123-A56-BU3}

%%
%% For managing citations, it is recommended to use bibliography
%% files in BibTeX format.
%%
%% You can then either use BibTeX with the ACM-Reference-Format style,
%% or BibLaTeX with the acmnumeric or acmauthoryear sytles, that include
%% support for advanced citation of software artefact from the
%% biblatex-software package, also separately available on CTAN.
%%
%% Look at the sample-*-biblatex.tex files for templates showcasing
%% the biblatex styles.
%%

%%
%% The majority of ACM publications use numbered citations and
%% references.  The command \citestyle{authoryear} switches to the
%% "author year" style.
%%
%% If you are preparing content for an event
%% sponsored by ACM SIGGRAPH, you must use the "author year" style of
%% citations and references.
%% Uncommenting
%% the next command will enable that style.
%%\citestyle{acmauthoryear}


%%
%% end of the preamble, start of the body of the document source.
\begin{document}

%%
%% The "title" command has an optional parameter,
%% allowing the author to define a "short title" to be used in page headers.
\title[HFR-AME]{Frequency Restoration and Modality Enforcement towards Resisting-corruption Multimodal Sentiment Analysis}
%%
%% The "author" command and its associated commands are used to define
%% the authors and their affiliations.
%% Of note is the shared affiliation of the first two authors, and the
%% "authornote" and "authornotemark" commands
%% used to denote shared contribution to the research.
\author{Weicheng~Xie}
\orcid{0000-0001-8946-7472}
\affiliation{%
  \institution{School of Computer Science \& Software Engineering, Shenzhen University}
  \city{Shenzhen}
  \country{China}}
\affiliation{%
  \institution{Guangdong Laboratory of Artificial Intelligence and Digital Economy (SZ)}
  \city{Shenzhen}
  \country{China}}
\affiliation{%
  \institution{Guangdong Provincial Key Laboratory of Intelligent Information Processing, Shenzhen University}
  \city{Shenzhen}
  \country{China}}
%\email{<EMAIL>}

\author{Haijian~Liang}
\affiliation{%
  \institution{School of Computer Science \& Software Engineering, Shenzhen University}
  \city{Shenzhen}
  \country{China}}
%\email{<EMAIL>}

\author{Zenghao~Niu}
\affiliation{%
  \institution{School of Computer Science \& Software Engineering, Shenzhen University}
  \city{Shenzhen}
  \country{China}}
%\email{<EMAIL>}


\author{Xianxu~Hou}
\affiliation{%
  \institution{School of AI and Advanced Computing, Xi’an Jiaotong-Liverpool University}
  \city{Suzhou}
  \country{China}}

\author{Siyang~Song}
\affiliation{%
  \institution{School of Computer Science, University of Exeter}
  \city{Exeter}
  \country{U.K.}}

\author{Zitong~Yu}
\affiliation{%
  % \institution{School of Computing and Information Technology, Great Bay University}
  \institution{Department of Computing and Information Technology, Great Bay University}
  \city{Dongguan}
  \country{China}}

\author{Linlin~Shen}
\affiliation{%
  \institution{Computer Vision Institute, School of Artificial Intelligence, Shenzhen University}
  \city{Shenzhen}
  \country{China}}
\email{<EMAIL>}

%\email{<EMAIL>}

%\email{<EMAIL>}



%%
%% By default, the full list of authors will be used in the page
%% headers. Often, this list is too long, and will overlap
%% other information printed in the page headers. This command allows
%% the author to define a more concise list
%% of authors' names for this purpose.
\renewcommand{\shortauthors}{Xie et al.}

%%
%% The abstract is a short summary of the work to be presented in the
%% article.
\begin{abstract}
For Multimodal Sentiment Analysis (MSA), previous methods concentrate on designing sophisticated fusion strategies and performing representation learning across heterogeneous modalities, aiming to leverage multimodal signals to detect human sentiment. However, these approaches fail to address the long-standing issue of corrupted modal details in videos, which maybe caused by the challenge of the 
%
% overmuch missing of crucial emotional semantics 
excessive loss of emotionally relevant semantics
%
resulted from the degradation of detailed information. In this work, we aim to improve the robustness capacity of resisting corruption in MSA, by introducing a Hierarchical Frequency Restoration and Adaptive Modality Enforcement (HFR-AME) approach. The HFR-AME progressively recovers blurred detailed cues in each modality while enhancing the discriminative power of modal representations. Specifically, to reconstruct distinct frequency band features, we propose to equip the HFR module with a key component called the Frequency Multimodal UNet (FM-UNet), so as to utilize complementary modal features as conditions. This meticulous restoration process, performed from low to high frequency, facilitates the comprehensive recovery of intricate details. Meanwhile, to adaptively integrate these diverse frequency features, we introduce the AME module to enhance the beneficial modal frequencies while suppressing irrelevant ones, with the goal of strengthening the restored modal representations. 
%
%
% Extensive experiments on the CMU-MOSI and CMU-MOSEI datasets demonstrate that our HFR-AME framework outperforms existing state-of-the-art MSA methods, achieving improvements of 0.5 and 0.6 percentage points in 7-class accuracy on the CMU-MOSI and CMU-MOSEI datasets, respectively.
Extensive experiments show our HFR-AME outperforms state-of-the-art methods on the CMU-MOSI and CMU-MOSEI datasets, improving 7-class accuracy by 0.5\% and 0.6\%, respectively. Further analysis also confirms its cross-lingual generalization and competitive computational efficiency.
%
Our code is made available at https://github.com/nianhua20/HFR-AME. 
\end{abstract}

%%
%% The code below is generated by the tool at http://dl.acm.org/ccs.cfm.
%% Please copy and paste the code instead of the example below.
%%
% \begin{CCSXML}
% <ccs2012>
%  <concept>
%   <concept_id>00000000.0000000.0000000</concept_id>
%   <concept_desc>Do Not Use This Code, Generate the Correct Terms for Your Paper</concept_desc>
%   <concept_significance>500</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>00000000.00000000.00000000</concept_id>
%   <concept_desc>Do Not Use This Code, Generate the Correct Terms for Your Paper</concept_desc>
%   <concept_significance>300</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>00000000.00000000.00000000</concept_id>
%   <concept_desc>Do Not Use This Code, Generate the Correct Terms for Your Paper</concept_desc>
%   <concept_significance>100</concept_significance>
%  </concept>
%  <concept>
%   <concept_id>00000000.00000000.00000000</concept_id>
%   <concept_desc>Do Not Use This Code, Generate the Correct Terms for Your Paper</concept_desc>
%   <concept_significance>100</concept_significance>
%  </concept>
% </ccs2012>
% \end{CCSXML}

\begin{CCSXML}
<ccs2012>
   <concept>
       <concept_id>10010147.10010178.10010224.10010245.10010251</concept_id>
       <concept_desc>Computing methodologies~Object recognition</concept_desc>
       <concept_significance>500</concept_significance>
       </concept>
 </ccs2012>
\end{CCSXML}

\ccsdesc[500]{Computing methodologies~Object recognition}

% \ccsdesc[500]{Do Not Use This Code~Generate the Correct Terms for Your Paper}
% \ccsdesc[300]{Do Not Use This Code~Generate the Correct Terms for Your Paper}
% \ccsdesc{Do Not Use This Code~Generate the Correct Terms for Your Paper}
% \ccsdesc[100]{Do Not Use This Code~Generate the Correct Terms for Your Paper}

%%
%% Keywords. The author(s) should pick words that accurately describe
%% the work being presented. Separate the keywords with commas.
\keywords{Multimodal Sentiment Analysis, Modality  Corruption, Frequency Restoration,  Multimodal Enforcement.}

\received{20 February 2007}
\received[revised]{12 March 2009}
\received[accepted]{5 June 2009}

%%
%% This command processes the author and affiliation and title
%% information and builds the first part of the formatted document.
\maketitle


\section{Introduction}
With the increasing expression of emotions and opinions by individuals on social networks, Multimodal Sentiment Analysis (MSA) has recently become a popular research area \cite{10.1145/3363560,zhu2023multimodal,das2023multimodal}. 
MSA aims to mine and comprehend video sentiments by leveraging data from different modalities, such as language, audio, and vision. 
Various modalities often complement each other, aiding in disambiguating information and offering supplementary clues to enhance the interpretative capacity. 
Nevertheless, user-generated videos in complex real-world scenarios are often imperfect, posing challenges for integrating heterogeneous modality sequences. 



\begin{figure}[htb]
    \centering
    \includegraphics[width=0.7\linewidth]{methods_imgs/motivation.pdf}
    \caption{Examples of corrupted modality details throughout the sequence: (a) an upward mouth movement in the visual modality can express positive emotions, but occlusion often obscures mouth details; (b) a low-pitched voice in the audio modality can signal negative emotions, yet recording videos from a distance cannot accurately capture the nuances of a person's voice; and (c) certain words in the text can partially convey the characters' emotions, but noisy environments can cause ambiguity in text recognition.}
    \label{fig:motivation}
\end{figure}

Due to various uncontrollable factors, modality details, defined as \textbf{fine-grained, emotionally salient cues}, may become obscured or corrupted.
For instance, visual corruption can result from the mouth being occluded
, obscuring a subtle smile (Fig. \ref{fig:motivation}a); audio corruption may involve background noise that distorts crucial vocal inflections (Fig. \ref{fig:motivation}b); and textual corruption can arise from transcription errors or ambiguous words (Fig. \ref{fig:motivation}c).
\textbf{The degradation of these details, which often reside in the high-frequency components of the signal, leads to a significant loss of crucial emotional semantics, as they are the primary carriers of nuanced sentiment.}
This absence can degrade model performance, hindering accurate sentiment analysis.


Current MSA approaches assume complete modality data, exhibiting significant limitations when faced with modality corruption. These methods primarily focus on modality fusion and representation learning, which aim to accurately interpret emotional states in videos by modeling both intra- and inter-modal dependencies. Most of the previous works are centered around developing sophisticated architectures and complex fusion strategies, including graph-based fusion \cite{article,6877623}, tensor-based fusion \cite{zadeh-etal-2017-tensor,liu-etal-2018-efficient-low} and attention-based fusion \cite{10601307, 9162150}. While these methods have demonstrated impressive performance under controlled conditions, they are fundamentally constrained by the persistent representation gap among inherently diverse modalities.  This gap primarily arises from the heterogeneous nature of modalities, where each modality exhibits distinct characteristics and structural properties. The presence of noise and data corruption further amplifies this gap, complicating the consistency and integration of multimodal features. More recent approaches for learning effective multimodal representations propose to decompose modalities into two disjoint parts, i.e. common and private representations, to extract commonalities among modalities and the specificity of each modality to benefit multimodal learning \cite{yang2022learning, yang2022disentangled}. Despite their theoretical appeal, these approaches face significant practical limitations. The decomposition process is highly sensitive to the quality and alignment of the input data, and the presence of corruption can severely compromise the integrity of both common and private representations. This sensitivity to data imperfections constitutes a substantial barrier to their deployment in real-world contexts, where data are frequently characterized by corruption.

Thus, this unsolved challenge motivates us to restore the corrupted fine-grained details within modal sequences. On the one hand, recent diffusion-based models \cite{dhariwal2021diffusion,ho2020denoising}, which utilize a UNet \cite{ronneberger2015u} as the underlying neural backbone, have demonstrated superior generative capabilities compared to previous methods. In particular, Rombach et al. \cite{rombach2022high} integrates transformers with the UNet backbone, enabling multimodal training. On the other hand, a series of studies, such as \cite{yin2019fourier}, employ deep learning from a frequency perspective. Some works \cite{zhu2023frequency,si2023freeu} combine spatial and frequency features within the UNet architecture to enhance generation capabilities. Liang et al. \cite{liang2023omni} found that different frequency bands contain various types of information, with low frequencies representing more semantic information and high frequencies capturing more detailed texture information. Furthermore, researches such as the F-Principle \cite{CiCP-28-1746} reveal that deep neural networks (DNNs) typically adapt to target functions spanning from low to high frequencies, throughout the training phase.

Most works in MSA generally assume the availability of complete modality data and rely on intricate fusion strategies, which are inadequate for preserving data integrity in the presence of modality corruption. In this work, we introduce a Hierarchical Frequency Restoration and Adaptive Modality Enforcement (HFR-AME) framework to resist the modality corruption, so as to mend corrupted detailed information for robust sentiment analysis. The HFR module, featuring a novel Frequency Multimodal UNet (FM-UNet), progressively reconstructs detailed information across low to high frequencies. Unlike conventional UNet architectures, our FM-UNet incorporates a Frequency Cross-modal Restoration block at both ends, leveraging complementary modal features as conditional guidance for frequency feature reconstruction. Additionally, the introduced Frequency Filter unit refines features transmitted through skip connections, enhancing the reconstruction capability of corrupted modal features. Our proposed AME module addresses the varying tendencies of DNNs towards different frequency information revealed in \cite{CiCP-28-1746}, by dynamically integrating diverse frequency band features,
which can not only enhance the beneficial semantics of modalities but also eliminate redundant features to obtain refined frequency representations. Our framework effectively resolves modality corruption issues while enhancing the discriminative capability of modality representations.
%
The remainder of this paper is organized as follows:
Section \ref{sec:related_work} reviews related work in multimodal sentiment analysis and frequency domain learning;
Section \ref{sec:proposed_approach} details the proposed HFR-AME framework; 
Section \ref{sec:experiments} presents our experimental setup, results, and detailed analyses; 
Section \ref{sec:complexity_analysis} compares the computational complexity of different methods;
Section \ref{sec:visualizations} provides visualizations to demonstrate our model's robustness;
Finally, Section \ref{sec:conclusion} concludes the paper and discusses the future work.

The main contributions of this work can be summarized as:
\begin{itemize}
    \item We propose the HFR-AME framework for resisting corruption in multimodal sentiment analysis (MSA). To the best of our knowledge, this is one of the first works to mitigate feature corruption in multimodal sequences via a frequency-based approach.
    \item HFR-AME comprises two innovative modules, i.e. the HFR module utilizes information from other supplementary modalities as conditions, to progressively restore modality features across different frequencies within the Frequency Multimodal UNet (FM-UNet), reconstructing missing details from low to high frequencies. The AME module efficiently integrates multimodal representations, by assigning adaptive weights to enhance relevant frequency components while suppressing less important ones. These fused components reinforce the recovered features, enabling more robust sentiment analysis.
    \item Extensive experiments on three benchmarking MSA datasets demonstrate the effectiveness of our proposed approach. Additionally, visualization results indicate that our model is a robust framework, which is capable of learning discriminative representations under different intensities of modality representation corruption.
\end{itemize}

\section{Related Work} \label{sec:related_work}

\subsection{Multimodal Sentiment Analysis}

Multimodal Sentiment Analysis (MSA) aims to predict sentiment polarity and sentiment intensity by leveraging information from language, audio, and vision modalities within video clips. Research on MSA primarily focuses on multimodal fusion and representation learning. For multimodal fusion, current mainstream methods emphasize sophisticated architectures and techniques \cite{zadeh-etal-2017-tensor, mai2019divide, 9162150, 9745163}. Previous approaches, such as the TFN \cite{zadeh-etal-2017-tensor}, employ outer product operations to iteratively generate joint representations for utterances. Additionally, RDFN\cite{9439177} separately captures high-level unimodal representations through a gyroscope structure and fuses these representations with a recurrent neural fusion architecture. 
Motivated by the significant achievements of the Transformer model \cite{vaswani2017attention} across diverse deep learning domains, some works adopt it for multimodal fusion \cite{tsai2019multimodal, 10124248,9713748, ZHU2023103223, sun2023efficient,hu-etal-2022-unimse,10250883,10146482}. For example, the MulT model \cite{tsai2019multimodal} leverages directional pairwise cross-modal attention to capture inter-modal information. AMGN \cite{10.1145/3388861} combines image and text features using visual-semantic attention and modality-gated LSTM, enhancing sentiment classification with semantic self-attention. UniMSE \cite{hu-etal-2022-unimse} employs modality fusion at structural and semantic levels, and utilizes contrastive learning to model sentiment-emotion relationships. The AMSA \cite{10.1145/3572915} integrates a multiscale attention model with slice positioning to capture modality correlations and a Transformer-based adaptive fusion mechanism. Moreover, DMLANet\cite{10.1145/3517139} designs multi-level attention mechanisms, including channel and spatial attention for visual features, semantic attention for text, and self-attention for sentiment-rich feature extraction. Nevertheless, the representation gap between inherently heterogeneous modalities largely affects cross-modal fusion. To bridge the modality gap, several approaches have been proposed to factorize modalities into complementary subspaces, thereby learning comprehensive multimodal representations \cite{hazarika2020misa, yang2022learning, yang2022disentangled}. For instance, Hazarika et al. \cite{hazarika2020misa} decompose each modality into modality-invariant and modality-specific representations, to capture both collective consistency and individual specificity of heterogeneous modalities. 

To provide a clear perspective on the current landscape, we summarize several representative MSA methods in Table~\ref{tab:related_work_comparison}. This comparison highlights a critical yet often unstated assumption in prior work, i.e. the availability of complete and uncorrupted modality data.
\textbf{(i)} Prominent fusion strategies (e.g., TFN~\cite{zadeh-etal-2017-tensor}, MulT~\cite{tsai2019multimodal}), while powerful, are designed under the implicit assumption of high-fidelity data without explicit detail restoration capabilities.
%
Their fusion mechanisms are inherently vulnerable to data corruption. For instance, the tensor outer product in TFN can amplify and propagate noise from corrupted features into the joint representation. Similarly, the cross-modal attention in MulT relies on meaningful feature similarities; corrupted inputs can lead to unreliable attention scores, causing the model to focus on irrelevant or noisy signals and thus degrading fusion performance.
\textbf{(ii)} More recently, research has begun to address data imperfections. Some works, like MissModal~\cite{lin2023missmodal}, focus on handling the complete absence of a modality stream by aligning representations from different modal subsets. Other methods, such as EMT-DLFR+\cite{sun2023efficient}, tackle feature-level imperfections by reconstructing features from randomly masked temporal segments.
\textbf{(iii)} 
% Although achieving encouraging results, 
However, these methods primarily concentrate on cross-modal interactions without directly addressing the issue of corrupted modality details. This degradation of fine-grained cues, often caused by real-world factors like occlusion or noise, remains a critical and unaddressed challenge. We thus design the HFR module to overcome this limitation by progressively restoring detailed features across various frequency bands during inter-modal interactions.

\begin{table*}[htbp]
\centering
\setlength{\tabcolsep}{3pt}  % 调整列间距
\footnotesize  % 或 \small, \scriptsize，根据需求选择
\caption{Summary  of representative Multimodal Sentiment Analysis (MSA) methods, in terms of the manner of using modality data, advantage, limitation and the role of handing modality corruption.
%focusing on their approaches to and limitations regarding modality corruption.
}
\label{tab:related_work_comparison}
\begin{tabular}{l|l|l|l|c}

\toprule
\textbf{Method} & \textbf{Focused Stage} & \textbf{Advantages} & \textbf{Key Limitation} & \textbf{\makecell{Handling \\ Modality \\ Corruption}}  \\

\midrule
TFN~\cite{zadeh-etal-2017-tensor} & Modality Fusion  & Explicit multimodal interaction & High-dimensional output & Partially  \\

\midrule
LMF~\cite{liu-etal-2018-efficient-low} & Modality Fusion  & Efficient training and inference & Performance dependent on rank selection & No  \\

\midrule
MulT~\cite{tsai2019multimodal} & Modality Fusion  & No alignment required, direct fusion & High computational cost & No \\

\midrule
MISA~\cite{hazarika2020misa} & Feature Representation  & Reducing modality gaps & Excessive reliance on the textual modality & No \\

\midrule
MMIM~\cite{han-etal-2021-improving} & Modality Fusion  & Reduces information loss & Misled by dominant modality & Partially \\

\midrule
EMT-DLFR+~\cite{sun2023efficient} & Modality Fusion  & Robust and efficient modality fusion  & Needs  complete modality data  for training & Yes \\

\midrule
MissModal~\cite{lin2023missmodal} & Modality Fusion  & Robustness with aligning modality & Excessive reliance on the textual modality & Yes \\

\midrule
C-MIB~\cite{9767641} & Feature Representation  & Effectively filters redundancy and noise & Depends on hyperparameters and fusion & Partially \\

\midrule
HyDiscGAN~\cite{inproceedings} & Modality Fusion & Efficient and privacy-preserving & Gap between generated and real features & No \\

\midrule
HFR-AME (Ours) & Feature Representation  & Enhanced robustness  by detail restoration  & Limited in extreme corruption & Yes \\

\bottomrule
\end{tabular}
\end{table*}


\subsection{Frequency Domain Learning}
Frequency-domain analysis has been a fundamental tool in conventional signal processing for decades \cite{baxes1994digital, pitas2000digital}. Recently, frequency-based operations such as the Fourier transform have been integrated into deep learning methods \cite{huang2021fsdr,xu2019training,pratt2017fcnn}. 
Recent works have introduced frequency information into the restoration domain to enhance image reconstruction \cite{9786841,10196308,MFSNet}. JWSGN \cite{9786841} recovers high-frequency details and enhance image quality by utilizing complementary frequency information and edge feature maps. 
% AIRFormer\cite{10196308} integrates a frequency-guided Transformer encoder with a frequency-refined decoder, leveraging learnable task-specific queries to restore degraded images. 
MFSNet \cite{MFSNet} introduces a multi-scale frequency selection network that integrates spatial and frequency domain knowledge, selectively recovering richer and more accurate information. However, these methods remain limited to intra-modal frequency interactions, restricting their recovery capability due to overlook of complementary multi-modal information.

Moreover, some studies examine the challenges of using UNet for generation from a frequency perspective \cite{zhu2023frequency,si2023freeu}. Zhu et al. \cite{zhu2023frequency} incorporates both spatial and frequency features into this architecture to generate examples, while preserving essential details. Huang et al. \cite{si2023freeu} utilize spectral modulation in the Fourier domain to adjust skip features, and re-balance the influences from the UNet's skip connections and backbone feature maps. Prior studies \cite{CiCP-28-1746,liang2023omni} emphasize diverse information across frequency bands, while DNNs adapt to target functions spanning low to high frequencies during training. Motivated by these findings, we propose the FM-UNet architecture to gradually restore the corrupted detail cues from low to high frequencies. Furthermore, we design the AME module to adaptively integrate features from different frequency bands to enhance modality representations.
This frequency-based strategy can be intuitively understood as reconstructing the global structure first and then refining local details.
%which is analogous to how humans process complex information in stages.


\begin{figure*}
\centering
\includegraphics[width=1.0\linewidth]{methods_imgs/simple_pipeline.pdf}
\caption{Overview of the Hierarchical Frequency Restoration and Adaptive
Modality Enforcement \textbf{(HFR-AME)}. It progressively restores features from low to high frequency bands by HFR and then adaptively fuses them by AME to resist modality corruption.}
\label{fig:simple_pipeline}
\end{figure*}

\section{Proposed Approach} \label{sec:proposed_approach}

The core of our approach is leveraging frequency decomposition to combat data corruption. Low-frequency components capture robust, global structures (e.g., face contours), while high-frequency components contain fine-grained, sentiment-rich details (e.g., subtle expressions) that are vulnerable to degradation. Our method is built on the rationale of using this stable low-frequency information to guide the restoration of vital yet fragile high-frequency cues.
%
Fig. \ref{fig:simple_pipeline} provides a high-level overview of this process. The framework first extracts initial unimodal features. Then, the Hierarchical Frequency Restoration (HFR) module progressively restores corrupted details from low to high frequency bands. Subsequently, the Adaptive Modality Enforcement (AME) module adaptively integrates these recovered frequency features into a robust, unified representation. This representation is finally fed to a classifier for sentiment prediction. The detailed architecture implementing this workflow is presented in Fig. \ref{fig:pipeline}.

Our task is to analyze the emotions expressed by characters in the video by leveraging multimodal data, including language ($l$), audio ($a$), and vision ($v$) modalities. For each video clip, the signals are denoted as sequences of low-level features ${X_m} \in \mathbb{R}^{T_m \times d_m}$, where $m \in \{l, a, v\}$, $T_m$ and $d_m$ are used to represent sequence length and feature dimension, respectively. Moreover, frequency refers to the rate at which a signal oscillates over time. Frequency band features represent the decomposition of a signal into different frequency ranges, where low frequencies capture global structures (e.g., overall shapes) and high frequencies capture fine details (e.g., textures or edges). Modal frequency refers to the frequency components of signals from different modalities (e.g., language, audio, and vision), each contributing distinct emotional cues.

To recover the intricate details missing from the modal sequence features, we utilize the designed Frequency Multimodal UNet (FM-UNet) within the HFR module, gradually reconstructing feature information across frequencies from low to high. In the AME module, the attention mechanism is utilized to extract critical frequency features, subsequently enforcing the restored modal features.  After that, we obtain a joint vector representation to predict the sentiment intensity score $y \in \mathbb{R}$. The subsequent sections elucidate the details of our HFR-AME framework.


\begin{figure*}
\centering
\includegraphics[width=1.0\linewidth]{methods_imgs/pipeline.pdf}
\caption{The overview framework of our resisting-corruption-oriented HFR-AME. In the HFR module, for the encoded low-level semantic features $H_{m}$ ($m\in\{l,a,v\}$), we employ complementary modal features as conditions to guide the restoration of various frequency band features $f_m^{[t]}$. We reconstruct the corrupted detailed information sequentially from low to high frequencies and store each restored frequency feature $\bar{f}_m^{[t]}$ in the frequency set $S_m$. Subsequently, in the AME module, we perform adaptive fusion on the different frequency features to make $F_m$ emphasize important frequency band representations.  $F_m$ is then used to enhance the restored modality features $H_m^{[T-1]}$ that is complete.} 
\label{fig:pipeline}
\end{figure*}


\subsection{Hierarchical Frequency Restoration (HFR)}
To enable each element of the input sequences to be aware of its neighboring elements, the three feature sequences are processed through a 1D temporal convolutional layer, which is formulated as follows:
\begin{equation}
  H_m=\operatorname{Conv1D}\left(\mathrm{X}_{m}, k_{m}\right) \in \mathbb{R}^{T_{m} \times d}
  \label{eq:conv1d}
\end{equation}
where $k_m$ is the size of the convolutional kernels for the modality $m \in \{l,a,v\}$, with all modal sequences being normalized to the same dimension $d$ to facilitate multimodal interaction and integration.
% where $k_m$ represents the size of the convolutional kernels for each modality. 
% $m \in \{l, a, v\}$, and all modal sequences are normalized to a uniform dimension $d$ to facilitate multimodal interaction and integration.

To comprehensively recover missing detailed information, we design the Frequency Multimodal UNet (FM-UNet), which leverages complementary features from other modalities to progressively restore current modality from low-frequency to high-frequency components. In this section, we detail the HFR module using vision sequences as an example.

\textbf{Hierarchical Frequency Extraction.}
%
The Hierarchical Frequency Restoration is an iterative process, which progresses from $t=0$ to $T-1$. In each iteration $t$, we use the frequency extractor to isolate a specific frequency band from the vision modality's feature representation $H_{v}^{[t]}$ at the current stage. This iterative process is designed to restore features from low-frequency bands to high-frequency ones progressively.
%
Specifically, we perform the Fast Fourier Transform $FFT(\cdot)$ to obtain the frequency features, and then isolate current frequency band information using a Fourier mask $mask_v^{[t]}$, and finally retrieve the specific frequency features through the inverse Fast Fourier Transform $IFFT(\cdot)$. Mathematically, these operations are performed as follows:
\begin{equation}
\begin{aligned}
& \mathcal{F}\left(H_{v}^{[t]}\right)=\operatorname{FFT}\left(H_{v}^{[t]}\right) \\
& \mathcal{F}^{\prime}\left(H_{v}^{[t]}\right)=\mathcal{F}\left(H_{v}^{[t]}\right) \odot  mask_v^{[t]} \\
& f_{v}^{[t]}=\operatorname{IFFT}\left(\mathcal{F}^{\prime}\left(H_{v}^{[t]}\right)\right)
\end{aligned}
\label{fft}
\end{equation}
where $\odot$ denotes element-wise multiplication. The Fourier mask, $mask_v^{[t]}$, is a pre-defined binary matrix that functions as a band-pass filter to isolate a specific frequency band. For a total of $T$ restoration steps, we design $T$ unique and disjoint masks ($mask_v^{[0]}$, $mask_v^{[1]}$, ..., $mask_v^{[T-1]}$) that partition the entire frequency spectrum into non-overlapping bands with equal width. Each mask  $mask_v^{[t]}$ corresponds to one of these bands, and the process progresses from low to high frequencies as $t$ increases. For any given iteration $t$, the same mask $mask_v^{[t]}$ is consistently applied across all modalities and data samples, and is also used within the Frequency Filter block, to ensure a focused and uniform restoration process for that specific frequency band.


\begin{figure*}
    \centering
\includegraphics[width=1.0\linewidth]{methods_imgs/FM-UNet.pdf}
    \caption{(a) Basic Structure of our FM-UNet.
    For the vision modality, language and audio modal features are utilized to restore and generate the frequency features of vision. (b) Frequency Cross-modal Restoration Unit. Damaged frequency features interact comprehensively with conditional features, leveraging complementary modal information to reinforce frequency features. (c) Frequency Filter Block. In %the Frequency Filter 
    this block, feature maps from the skip connection and the backbone undergo the re-weighting and filtering to enhance the restoration capability of the backbone.
    $\oplus$ and $\otimes$ represent element-wise addition and multiplication, respectively, and $\delta$ denotes the Sigmoid operation.}
    \label{generation}
\end{figure*}


As illustrated in Fig. \ref{generation}, the proposed FM-UNet enhances the conventional symmetric encoder-decoder architecture of UNet, by incorporating a Frequency Cross-modal Restoration unit at both ends of the network. This modification introduces guidance information from complementary modal features, facilitating effective frequency feature restoration. Moreover, a Frequency Filter unit is integrated into each skip connection to further strengthen the recovery capability of the backbone network. For the frequency representation $f_{v}^{[t]}$, we restore corrupted detailed information during the interaction, by leveraging the language and audio modalities within the HFR unit.

\textbf{Frequency Cross-modal Restoration.} In previous studies \cite{tsai2019multimodal, lv2021progressive}, only coarse-grained pairwise directional interactions between independent modalities were performed, failing to enhance the feature information across various frequency bands of the modalities. Emotional semantics are encoded heterogeneously across modalities, creating significant gaps in feature compatibility. FM-UNet addresses cross-modal semantic gaps, by incorporating Frequency Cross-modal Restoration (FCR) blocks within both the encoder and decoder stages. The FCR unit enhances interactions between modal representations across each frequency band, by simulating both inter-modality interactions and intra-modality dynamics. This integration encourages the accurate alignment of emotion-relevant features, and facilitates precise frequency reconstruction. Specifically, the FCR consists of multi-head attention layers, Layer Normalization (LN) layers, and position-wise feed-forward networks (FFN). Cross-Attention (CA) aims to leverage additional modality cues as source modality features to enhance the representation of the target modality, denoted as $H_s \in \mathbb{R} ^{T_s \times d_m}$ and $H_t \in \mathbb{R} ^{T_t \times d_t}$, respectively, where $s, t \in \{l,a,v\}$. The Queries are derived from the target modality, denoted as $Q_t = H_tW_Q$, while the Keys and Values are from the source modality, denoted as $K_s=H_sW_K$ and $V_s=H_sW_V$. The CA can be formulated as follows:
\begin{equation}
    CA(H_t,H_s) = softmax(\frac{Q_tK^{\top}_s}{\sqrt{d_k}})V_s
\end{equation}
where $\top$ represents the matrix transposition operation. In practice, we extend CA to Multi-Head Cross-Attention (MHCA) and Multi-Head Self-Attention (MHSA) to enable the model to attend to diverse sequential information. In the FCR unit, we enhance vision frequency features ($f_{v}^{[t]}$) by integrating the MHSA operation to capture inter-modal dependencies. Subsequently, we utilize language and audio features ($H_l^{[t]},H_a^{[t]}$) to interact with vision frequency features through cross-attention operations as follows:
\begin{equation}
\label{EqCrossAttenOper}
\begin{aligned}
 \hat{f'}^{[t]}_v = &MHSA(LN(f^{[t]}_v)) \\
 \hat{f''}_{v}^{[t]} = & MHCA(LN(H_l^{[t]}),LN(\hat{f'}^{[t]}_v)) + MHCA(LN(H_a^{[t]}),LN(\hat{f'}^{[t]}_v)) \\
 \hat{f}^{[t]}_v =& FFN(LN(\hat{f''}^{[t]}_v)) + \hat{f''}^{[t]}_v
\end{aligned}
\end{equation}
Consequently, features in each frequency band can fully interact with other modalities, thereby utilizing the acquired complementary information for the fine-grained restoration.

\textbf{Frequency Filter.} Sentiment information exhibits variability across distinct frequency bands, demanding adaptive filtering mechanisms to retain critical cues while mitigating redundancy. The Frequency Filter (FF) module within FM-UNet exclusively transmits band-specific frequency features through skip connections, and adaptively integrates them with the backbone representations, enhancing reconstruction capabilities. Technically, for the $i$-th block of the FM-UNet decoder, we define $p_i$ and $q_i$ as the features corresponding to the backbone network and the skip connection, respectively. 
By replacing $H_v^{[t]}$ with $q_i$ in Eq. \eqref{fft} and applying the Hierarchical Frequency Extraction, the frequency feature $f_{q_i}^{[t]}$ can be obtained, which is used in the following dynamic filter operation:
% The operation for $q_i$ described in Eq. \eqref{fft} is then employed to obtain the frequency feature $f_{q_i}^{[t]}$
\begin{equation}
\begin{aligned}
& w_{p} = \sigma (\mathcal{F}_p(p_i,\theta_p) + \mathcal{F}_q(f_{q_i}^{[t]}, \theta_q))\\
& U^{[i]} = w_{p} \odot p_i + (1-w_{p}) \odot f_{q_i}^{[t]}
\end{aligned}
\end{equation}
where $\sigma$ denotes the Sigmoid non-linearity function, $\mathcal{F}_p$ and $\mathcal{F}_q$ are fully connected layers with parameters $\theta_p$ and $\theta_q$, respectively. The fused feature $U^{[i]}$ is utilized in subsequent layers of the UNet architecture. After the encoding and decoding operations in FM-UNet, we obtain the frequency features $\bar{f}_v^{[t]}$ with the detailed cues being restored. These features are added to the modality features, to generate the restored features for the next higher frequency band as:
\begin{equation}\label{EqHv}
    \begin{aligned}
        & H_v^{[t+1]} = H_v^{[t]} + \psi_v(f_v^{[t]}) 
    \end{aligned}
\end{equation}
where $\psi_v$  denotes the FM-UNet for the vision modality. 

Preserving emotional semantics across multiple modalities during frequency restoration is a significant challenge. To address this, the FM-UNet framework incorporates a margin loss, to penalize deviations between the reconstructed and original frequency features that exceed a predefined threshold. This loss facilitates feature reconstruction by integrating additional relevant semantic information from other modalities. To this end, we define a margin loss as:
\begin{equation}
    \mathcal{L}_{mar_v}=\sum_{t=0}^{T-1}  \left |  cos(f_v^{[t]},\bar{f}_v^{[t]} )- \alpha \right |
\end{equation}
where $cos(\cdot,\cdot)$ denotes the cosine similarity between two feature representations. $\alpha$ is a distance margin for all three modalities, i.e., a hyper-parameter ranging from 0 to 1.



\subsection{Adaptive Modality Enforcement (AME)}
\textbf{Adaptive Frequency Fusion.} After $T$ steps of frequency feature restoration, and progressing from low to high frequencies within the HFR module, we obtain enhanced feature representations across various frequency bands. Motivated from that features from different frequency bands exhibit varying levels of importance in \cite{CiCP-28-1746}, we thus propose the AME module to dynamically enhance frequency features beneficial to emotional semantics, and reduce the impact of irrelevant frequency features, thereby obtaining a refined frequency-based representation.

Concretely, for the given repaired frequency set \( S_m = \{\bar{f}_m^{[0]}, \bar{f}_m^{[1]}, \dots, \bar{f}_m^{[T-1]}\} \), our AME module assigns a normalized weight to each frequency feature to emphasize those that enhance emotional clues. We compute the weights using a fully connected layer \( \mathcal{F}_m \) with learnable parameters \( \theta_m \), apply a Softmax for normalization, and then aggregate the weighted features as follows:
\begin{equation}\label{fuse_freq}
\begin{aligned}
    \mathbf{w}_m &= \text{Softmax}\left(\mathcal{F}_m\left(S_m; \theta_m\right)\right), \\
    F_m &= \sum_{t=0}^{T-1} w_m^{[t]} \cdot \bar{f}_m^{[t]},
\end{aligned}
\end{equation}
where \( \mathbf{w}_m = \{w_m^{[0]}, w_m^{[1]}, \dots, w_m^{[T-1]}\} \) represents the normalized weights for each frequency band.




\textbf{Modality Frequency Enforcement.} 
% After enhancing each modality feature $\bar{H}_m = H^{[T-1]}_m$ by restoring full-band features in Eq. \eqref{EqHv}, we use integrated frequency features $F_m$ to further enhance them, aiming to leverage emotionally relevant frequency features for sentiment analysis, where the direct fusion method, i.e. the element-wise addition of the two representations, is used.
After enhancing each modality feature $H^{[T-1]}_m$ by restoring full-band features in Eq. \eqref{EqHv}, we further enhance them using integrated frequency features $F_m$. This leverages emotionally relevant frequency features for sentiment analysis through direct fusion, where element-wise addition of the two representations is applied.
 %We perform element-wise addition of the two representations, a simple and direct fusion method. 
 Then, we use the multi-head self-attention (MHSA) operation to summarize the reinforced modality features into global representations. Finally, we concatenate the three modality features as the representation to obtain the final prediction of the sentiment intensity, via a multi-layer perceptron (MLP):
\begin{equation}\label{enforce_mod}
\begin{aligned}
& h_m = MHSA(\bar{H}_m + F_m)\\
& \hat{y} = MLP([h_l,h_v,h_a])
\end{aligned}
\end{equation}
where $[,\cdot,]$ means feature concatenation. Mean Absolute Error (MAE) loss is applied for the regression of sentiment labels, which is formulated as:
\begin{equation}
    \mathcal{L}_{\text {task }}=\frac{1}{N} \sum_{i=1}^{N}\left|y^{i}-\hat{y}^{i}\right|
\end{equation}
where $y^{i}$ and $\hat{y}^{i}$ are the ground truth and predicted labels of the $i$-th sample, respectively.

\renewcommand{\algorithmicrequire}{\textbf{Input:}}
\renewcommand{\algorithmicensure}{\textbf{Output:}}

\begin{algorithm}[htbp]
\caption{Training of our HFR-AME}
\label{alg}
\begin{algorithmic}[1]
    \Require The sequence features processed through a 1D temporal convolution layer: $H_{m} \in \mathbb{R}^{T_m \times d}, m \in \{l,a,v\}$, and the number of iterations $T$ for repairing frequency band features.
    \Ensure Predicted sentiment intensity $\hat{y}$
    
    \State Initialize modality features: $H_{m}^{[0]} = H_{m}$ for all $m \in \{l, a, v\}$
    
    \For{$t = 0$ to $T-1$}
        \State Extract current frequency band features $f_{m}^{[t]}$ from low to high frequencies using Eq.~\eqref{fft}
        \State Restore and enhance frequency feature $f_{m_1}^{[t]}$ using the other two modality features ($H_{m_2}^{[t]}, H_{m_3}^{[t]}$) as conditions, where $m_1, m_2, m_3 \in \{l, a, v\}$ and $m_1 \neq m_2 \neq m_3$, according to Eq.~\eqref{EqCrossAttenOper}
        \State Update the modal sequence features using the reconstructed features $\bar{f}_{m}^{[t]}$ from the FM-UNet through Eq.~\eqref{EqHv}
    \EndFor
    
    \State Aggregate frequency features within each modality to obtain $F_{m}$ using the attention mechanism in Eq.~\eqref{fuse_freq}
    \State  Enhance the repaired modality features $H_{m}^{[T-1]}$ using fused frequency features $F_m$, and use it to obtain the final predictions $\hat{y}$ according to Eq.~\eqref{enforce_mod}
    % \State Enhance the repaired modality features $H_{m}^{[T-1]}$ using fused frequency features $F_m$ according to Eq.~\eqref{enforce_mod}
    % \State Compute the final predictions $\hat{y}$
\end{algorithmic}
\end{algorithm}

Consequently, the overall loss function is formulated as follows:
\begin{equation}
    \mathcal{L}=\mathcal{L}_{\text {task }} + \lambda (\mathcal{L}_{mar_l}+\mathcal{L}_{mar_v}+\mathcal{L}_{mar_a})
\end{equation}
where $\lambda \in \mathbb{R}$ is the weight balancing the contribution of frequency feature repair loss to the overall loss $\mathcal{L}$. For clarity, Alg. \ref{alg} outlines the training procedure of our HFR-AME.


\section{Experiments} \label{sec:experiments}
\subsection{Datasets and Metrics}
In this paper, we first utilize two publicly available multimodal sentiment analysis datasets: CMU-MOSI\cite{zadeh2016multimodal} and CMU-MOSEI\cite{zadeh2018multimodal}. \textbf{CMU-MOSI} is a widely used multimodal dataset comprising 2,199 samples of short monologue video clips, which are extracted from 93 YouTube movie review videos and 89 distinct speakers. Each sample is annotated with sentiment scores ranging from -3 (strongly negative) to 3 (strongly positive). \textbf{CMU-MOSEI} is a larger dataset than CMU-MOSI, containing 23,454 YouTube monologue video segments, which cover 250 distinct topics from 1,000 distinct speakers. The utterances in the dataset are randomly selected from a variety of movie review topics. Each utterance is annotated with sentiment scores ranging from -3 to +3 and categorized into six different emotion classes. In addition, the \textbf{CH-SIMS} dataset~\cite{yu2020sims} is used to assess the generalization and language-agnostic capabilities of our method. CH-SIMS is a challenging Chinese multimodal sentiment analysis dataset featuring 2,281 fine-grained annotated video clips from diverse real-world scenarios. It is specifically designed to capture complex, culturally-specific sentiment expressions, and its annotations support multi-level sentiment classification (e.g., 2-class, 5-class), making it an applicable benchmark for testing a model's robustness and cross-lingual generalization.
% The basic statistics are shown in Table \ref{datasets}. 

Sentiment intensity prediction on the three datasets involves regression tasks, with metrics including mean absolute error (MAE) and Pearson correlation (Corr). The benchmark assessment also incorporates classification scores, including seven-class accuracy (Acc-7, indicating correct sentiment label predictions in the range of [-3, +3]), binary accuracy (Acc-2), and F1-Score. Specifically, we calculate Acc-2 and F1-Score in two ways: negative / non-negative (non-exclude zero)  and negative / positive (exclude zero).


\subsection{Implementation Details}
To ensure a fair comparison, we utilize the officially aligned features provided by the respective benchmark datasets, which are widely adopted by the state-of-the-art (SOTA) methods. Recently, Transformer-based pre-trained language models have achieved significant success across various tasks in natural language processing \cite{young2018recent}. For the language modality, we employ the pre-trained BERT-base-uncased model \cite{devlin2018bert} to extract a sequence of 768-dimensional token representations. To extract vision features, we use Facet \cite{baltruvsaitis2016openface} to obtain 35 facial action units, which record facial muscle movements to represent emotions. The dimension of the vision features extracted is 20 for CMU-MOSI and 35 for CMU-MOSEI. Furthermore, the acoustic features comprise a range of low-level statistical audio functions extracted from COVAREP \cite{degottex2014covarep}, with a dimension of 74 for both datasets.

For CMU-MOSI and CMU-MOSEI, we set the number of epochs to 30 and the batch size to 32 to expedite training. The number of output channels ($d$) of Conv1D is set to 128, and the kernel size is set to (1, 5, 3). We utilize the AdamW optimizer with a learning rate of $2e^{-5}$ for the CMU-MOSI dataset and $1e^{-5}$ for the CMU-MOSEI dataset, followed by the implementation of a cosine decay strategy to update the learning rate. FM-UNet consists of the designed autoencoder with a size of 128-64-32-64-128. For the CMU-MOSI and CMU-MOSEI benchmarks, we set the hyper-parameter $T$ to 5 and 3, the trade-off parameter $\lambda$ to 0.1, and the margin factor $\alpha$ to 0.4 and 0.5, respectively. All hyper-parameters are determined using the validation set. Our proposed model was implemented using PyTorch on an RTX 3090 GPU with the memory of 24 GB.

Regarding the  choices of these hyperparameters, %we provide further elaboration to address their rationale. 
the number of training epochs was set to 30, as our empirical results on the validation set indicated that the model's performance consistently converged at this point, i.e., further training yields no significant gains or potentially leads to overfitting. %For the critical hyperparameters of our HFR-AME framework, i.e., 
Regarding the number of restoration iterations \(T\) in our HFR-AME, the margin \(\alpha\), and the loss weight \(\lambda\), their optimal values (\(T\)=5/3, \(\alpha\)=0.4/0.5, \(\lambda\)=0.1) were determined through a systematic grid search on the validation set, which are validated in our sensitivity analysis in Section \ref{sec:hyper_parameters},  and Fig. \ref{TTTaccuracy} in  Appendix.


\begin{table*}[htbp]
    \centering
    \caption{Performance comparison between HFR-AME and the state of the arts on CMU-MOSI and CMU-MOSEI datasets. In Acc2 and F1, the left of the “/” corresponds to “negative/non-negative” and the right corresponds to “negative/positive”. $^\dag$: results are reproduced using publicly available source codes.}
    \resizebox{\textwidth}{!}{
    
	\begin{tabular}{l|ccccc|ccccc}
        \toprule
		\multirow{2}{*}{Approaches} & \multicolumn{5}{c|}{CMU-MOSI} & \multicolumn{5}{c}{CMU-MOSEI}\\
		& Acc7$\uparrow$  & Acc2$\uparrow$ & F1$\uparrow$ & MAE$\downarrow$ & Corr$\uparrow$ & Acc7$\uparrow$  & Acc2$\uparrow$ & F1$\uparrow$ & MAE$\downarrow$ & Corr$\uparrow$\\ 
            \midrule
        TFN\cite{zadeh-etal-2017-tensor} & 33.7  &  78.3 / 80.2  &  78.2 / 80.1  & 0.925  & 0.662  &  
		52.2 & 81.0 / 82.6  &  81.1 / 82.3  & 0.570 & 0.716 \\
        LMF\cite{liu-etal-2018-efficient-low} & 32.7  &  77.5 / 80.1  &  77.3 / 80.0  & 0.931  & 0.670  & 
		52.0 & 81.3 / 83.7  &  81.6 / 83.8  & 0.568 & 0.727 \\
        MFN\cite{DBLP:journals/corr/abs-1806-06176} & 34.2 &  77.9 / 80.0  &  77.8 / 80.0 & 0.951 & 0.665  & 
		51.1 & 81.8 / 84.0  &  81.9 / 83.9  & 0.575 & 0.720 \\
        Graph-MFN\cite{zadeh2018multimodal}& 34.4 &  77.9 / 80.2  &  77.8 / 80.1 & 0.939 & 0.656  & 
		51.9 & 81.9 / 84.0  &  82.1 / 83.8  & 0.569 & 0.725 \\
        MFM\cite{zadeh2018memory} & 33.3 &  77.7 / 80.0  &  77.7 / 80.1 & 0.948 & 0.664  &  
		50.8 & 80.3 / 83.4  &  80.7 / 83.4  & 0.580 & 0.722 \\
        MCTN\cite{pham2019found} & 33.7 &  78.7 / 80.0  &  78.8 / 80.1 & 0.960 & 0.686  & 
		52.0 & 80.4 / 83.7  &  80.9 / 83.7  & 0.570 & 0.728 \\
        MulT\cite{tsai2019multimodal} & 35.0 &  79.0 / 80.5  &  79.0 / 80.5  & 0.918 & 0.685  &
		52.1 & 81.3 / 84.0  &  81.6 / 83.9  & 0.564 & 0.732 \\
        MISA\cite{hazarika2020misa} & 43.5 &  81.8 / 83.5  &  81.7 / 83.5  & 0.752 & 0.784 & 
		52.2 & 81.6 / 84.3  &  82.0 / 84.3  & 0.550  & 0.758 \\
        MAG-BERT\cite{rahman2020integrating} & 45.1  &  82.4 / 84.6  &  82.2 / 84.6  &  0.730 & 0.789 &
		52.8 &  81.9 / 85.1  &  82.3 / 85.1  & 0.558  & 0.761 \\
        Self-MM\cite{yu2021learning} &  45.8 &  82.7 / 84.9  &  82.6 / 84.8  & 0.731  & 0.785  & 
		53.0 &  82.6 / 85.2  &  82.8 / 85.2  &  0.540 & 0.763 \\
        MMIM\cite{han-etal-2021-improving} & 45.0 &  83.0 / 85.1  &  82.9 / 85.0  & 0.738  & 0.781  & 
		53.1 &  81.9 / 85.1  &  82.3 / 85.0  & 0.547  &  0.752 \\
        FDMER\cite{yang2022disentangled} & 44.1 &  --- / 84.6  &  --- / 84.7  & 0.724  & 0.788  & 
		54.1 &  --- / 86.1  &  --- / 85.8  & 0.536  &  \underline{0.773} \\
        EMT-DLFR$^\dag$\cite{sun2023efficient} & 46.1 & 82.7 / 84.6 & 82.6 / 84.6 & 0.710 & \underline{0.797} & 
            53.3 & 83.8 / 86.1 & \underline{84.1} / \underline{86.0} & \underline{0.530} & \underline{0.773} \\
        MissModal\cite{lin2023missmodal} & 47.2 &  \underline{84.1} / \underline{86.1}  &  \underline{84.0} / 86.0  & \underline{0.698}  & \textbf{0.801}  & 
		53.9 &  83.6 / 85.8  &  83.6 / 85.8  & 0.533  &  0.769 \\ 
        C-MIB\cite{9767641} & \underline{48.2} &  --- / 85.2 &  --- / 85.2  & 0.728  & 0.793  
            & 53.0 & --- / \underline{86.2} & --- / \textbf{86.2} & 0.584 & \textbf{0.789} \\ 
        HyDiscGAN\cite{inproceedings} & 43.2 &  81.9 / \textbf{86.3} &  83.7 / \textbf{86.3}  & 0.749  & 0.782  & 
            \underline{54.4} &  81.9 / \textbf{86.3} &  82.1 / \textbf{86.2}  & 0.533  &  0.761 \\ 
        CASP \cite{guo2025casp} & 34.4 &  78.1 / 79.3 &  78.0 / 79.2  & 0.950  & 0.665  & 
            52.9 &  \underline{83.8} / 83.9 &  83.5 / 83.3  & 0.558  &  0.726 \\ 
        \midrule
		HFR-AME (Ours) & \textbf{48.7}  &  \textbf{84.3} / \textbf{86.3}  &  \textbf{84.2} / \underline{86.2}  &  \textbf{0.691} & \textbf{0.801}  & \textbf{55.0}  &  \textbf{84.5} / \textbf{86.3}  &  \textbf{84.6} / \textbf{86.2}  & \textbf{0.529}  &  0.769 \\
        \bottomrule
	\end{tabular}
    }
	\label{results}
	
\end{table*}



\subsection{Comparison with the state of the arts}


We compare HFR-AME with the following SOTA models, i.e., TFN \cite{zadeh-etal-2017-tensor}, LMF \cite{liu-etal-2018-efficient-low}, MFM \cite{DBLP:journals/corr/abs-1806-06176}, Graph-MFN \cite{zadeh2018multimodal}, MFN \cite{zadeh2018memory}, MCTN \cite{pham2019found}, MulT \cite{tsai2019multimodal}, MISA \cite{hazarika2020misa}, MAG-BERT \cite{rahman2020integrating}, Self-MM \cite{yu2021learning}, MMIM \cite{han-etal-2021-improving}, FDMER \cite{yang2022disentangled}, EMT-DLFR \cite{sun2023efficient}, MissModal \cite{lin2023missmodal}, C-MIB \cite{9767641}, HyDiscGAN \cite{inproceedings} and CASP \cite{guo2025casp} under the same dataset settings in MSA.

We compare HFR-AME with these SOTA models on the CMU-MOSI and CMU-MOSEI datasets, with the comparative results detailed in Table \ref{results}. The proposed HFR-AME achieves superior MSA accuracy and demonstrates the best performance, compared with all the SOTA  models on both datasets across most metrics. 
%Specifically, HFR-AME outperforms all SOTA baseline models on both CMU-MOSI and CMU-MOSEI datasets across most metrics.
Compared to models with sophisticated modality fusion mechanisms, such as TFN, LFN, and MulT, our proposed HFR-AME consistently improves performance. This indicates that simultaneously fusing modalities and repairing distinct frequency band features facilitates the learning of multimodal representations.

Our model surpasses MissModal across all metrics on both datasets. While MissModal addresses missing modalities by constructing three constraints, our approach focuses on resolving the issue of lost details in the sequence. HFR-AME shows improvements of 1.5\% and 1.1\% in terms of Acc7, 0.2\% and 0.5\% in terms of Acc2, and 0.2\% and 0.4\% in terms of F1-score on CMU-MOSI and CMU-MOSEI, respectively. Compared to C-MIB, which filters out noise and redundancy through the information bottleneck principle, our method achieves improvements of 0.5\% and 2.0\% in terms of Acc7 on the CMU-MOSI and CMU-MOSEI datasets, respectively. These promising outcomes can be attributed to the effectiveness of different frequency band feature restorations, which leverage cross-modal interactions to guide fine-grained recovery within modalities. Moreover, our model surpasses the best-performing HyDiscGAN, i.e., a hybrid distributed collaborative learning approach, in terms of accuracy metrics including Acc7, MAE, and Corr. Notably, we observe large improvements of 5.5\% and 0.6\% in terms of Acc7 on CMU-MOSI and CMU-MOSEI, and improvements of 5.8\% and 0.4\% in terms of MAE, as well as 1.9\% and 0.8\% in terms of Corr on these datasets. This demonstrates the importance of learning robust representations of modality features and the effectiveness of our HFR-AME.

\subsection{Cross-Lingual Generalization Analysis}

\begin{table*}[htbp]
    \centering
    \caption{Performance comparison between HFR-AME and the state of the arts on CH-SIMS.}
    \begin{tabular}{l|cccccc}
        \toprule
        Approaches & Acc2$\uparrow$  & Acc3$\uparrow$ & Acc5$\uparrow$ & F1$\uparrow$ & MAE$\downarrow$ & Corr$\uparrow$\\ 
        \midrule
        TFN\cite{zadeh-etal-2017-tensor}        & \underline{80.1} & \underline{67.2} & \underline{42.7} & 59.3 & 43.8 & 42.1 \\
        MFN\cite{DBLP:journals/corr/abs-1806-06176}  & 74.4 & 62.4 & 36.1 & 52.8 & 46.5 & 45.8 \\
        MFM\cite{zadeh2018memory}               & 73.5 & 64.9 & 36.5 & 51.8 & 47.8 & 48.2 \\
        MulT\cite{tsai2019multimodal}           & 78.9 & 65.7 & 41.7 & 58.1 & 44.7 & 44.7 \\
        MMIM\cite{han-etal-2021-improving}      & 77.7 & 61.7 & 40.3 & 54.8 & 44.0 & 43.8 \\
        EMT-DLFR\cite{sun2023efficient}         & 78.8 & 65.6 & 40.8 & \underline{79.0} & \underline{43.2} & \textbf{58.7} \\
        CASP \cite{guo2025casp}                 & 78.3 & \textbf{67.2} & 42.5 & 78.0 & 43.6 & 56.8  \\ 
        \midrule
        HFR-AME (Ours)                          & \textbf{80.5} & 65.2 & \textbf{46.0} & \textbf{80.0} & \textbf{41.2} & \underline{58.2}  \\
        \bottomrule
    \end{tabular}
    % }
    \label{results-sims}
\end{table*}


To assess the generalization and language-agnostic capabilities of our method, we compare our HFR-AME with several representative MSA methods on \textbf{CH-SIMS}~\cite{yu2020sims}, as shown in Table~\ref{results-sims}. It shows that our model demonstrates superior cross-lingual generalization on this Chinese dataset, achieving the highest scores in four critical metrics: Acc2 (80.5\%, +0.4\% over TFN), Acc5 (46.0\%, ****\% over TFN), F1-score (80.0\%, ****\% over EMT-DLFR+), and MAE (41.2, 2.0\% improvement over EMT-DLFR+). Notably, while CASP shows competitive performance in Acc3 (67.2\%), our method maintains balanced excellence across all evaluation dimensions, i.e. a crucial advantage for real-world applications where different metrics may be prioritized. This overall strong performance demonstrates the promise of our frequency-based restoration approach for addressing language-specific corruption patterns beyond English.


\subsection{Ablation study} \label{sec:ablation}

\subsubsection{Evaluation on Components}
We conduct ablation studies on the CMU-MOSEI dataset to assess the effectiveness of different modules in our HFR-AME framework. We explore the contribution of each component by excluding it from the model, as shown in Table \ref{ablation-module}.

\begin{table}[htbp]
\centering
\setlength\tabcolsep{4.5pt}
\caption{Ablation study of HFR-AME's components on CMU-MOSEI. The experiments are set to study the contributions of individual components from two major modules.}
\label{ablation-module}
\begin{tabular}{l|ccccc}
    \toprule
    \quad Description & Acc7$\uparrow$  & Acc2$\uparrow$ & F1$\uparrow$ & MAE$\downarrow$ & Corr$\uparrow$ \\
    \midrule
    \multicolumn{6}{l}{(1) HFR}\\
    w/o $\text{FM-UNet}_{\text{FCR}}$  & \underline{54.3} &  82.8 / 85.9  &  83.0 / 85.7  & 0.546  & 0.752 \\
    w/o $\text{FM-UNet}_{\text{FF}}$   & 52.8  &  76.3 / 81.0  &  77.1 / 81.1  & 0.544  & 0.753 \\
    w/o $\text{FM-UNet}$   & 54.0  &  81.8 / 85.8  &  82.4 / 85.7  & 0.543  & 0.758 \\
    w/o $\mathcal L_{mar}$      & 53.7  &  \underline{83.8} / \underline{86.2}  &  \underline{84.0} / \underline{86.1}  & \underline{0.542}  & \underline{0.759} \\
    \midrule
    \multicolumn{6}{l}{(2) AME}\\
    w/o AFF & 52.0  &  81.7 / 84.7  &  82.0 / 84.6  & 0.550 & 0.755 \\
    w/o MFE & 52.2  &  82.2 / 84.9  &  82.6 / 84.8  & 0.546 & 0.756 \\
    \midrule
     (3) HFR-AME & \textbf{55.0}  &  \textbf{84.5} / \textbf{86.3}  &  \textbf{84.6} / \textbf{86.2}  & \textbf{0.529}  &  \textbf{0.769} \\
    \bottomrule 
\end{tabular}
\end{table} 

In the \textbf{HFR module}, to validate the generative capability of our FM-UNet architecture, we investigate the contribution of each component in it. (i) We remove the Frequency Cross-modal Restoration (FCR) block in UNet (see the case `w/o $\text{FM-UNet}_{\text{FCR}}$'), resulting in a slight performance drop. This highlights the importance of capturing dependencies within and across modalities in feature learning, as leveraging complementary information and additional contextual clues enhances modal features. (ii) Removing the Frequency Filter (FF) block in FM-UNet (the case `w/o $\text{FM-UNet}_{\text{FF}}$') leads to a significant decrease in performance, with ACC2 dropping by 5.3\% and F1 decreasing by 5.1\%. This suggests that skip features could heavily influence decoder features in the absence of the frequency filter block, potentially overshadowing the backbone's generative capability. 
(iii) Furthermore, the exclusion of the FM-UNet module (the case `w/o $\text{FM-UNet}$') results in a large decline in model performance, with Acc2 decreasing by 2.7\% and the F1 score by 2.2\%. This finding indicates that, within the FM-UNet architecture, frequency features effectively utilize information from other modalities to achieve cross-modal restoration, underscoring its critical contribution in improving overall performance.
(iv) We also remove the margin loss $\mathcal{L}_{mar}$ and observe a slight performance drop across all metrics. This result suggests that retaining modality-specific information is crucial for learning a comprehensive multimodal representation when repairing and enhancing frequency features.


In the \textbf{AME module}, (i) we substitute the attention fusion module with the element-wise addition method that directly merges the features without considering their attention weights in the Adaptive Frequency Fusion (AFF) unit (see the case `w/o AFF'). This substitution resulted in a noticeable performance drop, highlighting the pivotal role of the attention fusion module in emphasizing essential frequency components while mitigating potentially adverse frequency band features. (ii) Additionally, removing the Modality Frequency Enforcement (MFE) module (see the case `w/o MFE'), results in a slight performance decline, suggesting the necessity of fused frequency features for enhancing the feature representation. Meanwhile, it enables feature representation to focus more on important frequency clues to learn discriminative features.


\subsubsection{Role of Modalities}
To elucidate the roles of different modalities in sentiment analysis, we remove one modality at a time to assess its impact on performance for the CMU-MOSEI dataset. To simulate the complete absence of a modality, the feature sequence of that modality is replaced with a zero-valued sequence. The complete absence of a modality is simulated by replacing its feature sequence with a zero-valued sequence. These modified features are subsequently processed by the HFR-AME network, where each frequency band feature of the modality features is restored and enhanced.

As depicted in Table \ref{modality-lost}, we can observe that the performance drops when each modality is removed,  indicating that all the three modalities contribute positively to the performance of HFR. When restoring modal frequency features, leveraging a broader array of complementary modal features as guidance is beneficial for enhancing cross-modal interactions. The most notable decrease in performance occurs when the language modality is eliminated, highlighting its pivotal role in MSA tasks compared to visual and audio modalities, this maybe because the text modality contains a wealth of emotional cues.


% \subsubsection{Selection of Iteration Time $T$}
% Fig. \ref{TTTaccuracy} (a) shows that our model achieves the best performance across all metrics, when the frequency band features are divided into three distinct modal features (i.e., $T$ is set to 3) for restoration. When $T$ is set to too small a value, some complex information in the modal features becomes difficult to regenerate directly, during the overall repair process. When $T$ is too large, %the performance of the model starts to decline. The reason for this decline is that
% the limited understanding of the majority of other frequency bands hampers the repair capability,
% when dividing the modal features into multiple frequency band features, and restoring a specific frequency band feature. 
% Thus,  in order to achieve an accurate restoration of a specific frequency band feature, it is essential to ensure that each frequency band feature contains sufficient modal information, when the modal features are divided into different frequency bands.

% \subsubsection{Hyper-parameter Analysis of Margin $\alpha$}
% To assess the performance sensitivity of restoration against the margin hyper-parameter $\alpha$, we conducted experiments under varying $\alpha$, as shown in Fig. \ref{TTTaccuracy} (b).

% Fig. \ref{TTTaccuracy} (b) shows that our model achieves the optimal performance when the margin $\alpha$ is set to 0.4. When the margin value exceeds 0.4, there is a slight decrease in the model's performance. This limitation occurs 
% when the restored frequency features are similar to the corrupted ones, hindering the extraction of complementary clues from other modalities, during recovering missing details and enhancing existing information. When the margin value falls below 0.4, a notable decline in the model's performance is observed. This is attributed to the need to incorporate guidance from other modality features, in encouraging alignment with the emotional context of the modality, during the restoring of frequency features. Otherwise, the modality will learn emotion-independent cues from other modalities.


\begin{table}[htbp]
\centering
% \setlength\tabcolsep{4.5pt}
\caption{Ablation results specific to different modalities on the CMU-MOSEI dataset.}
% \label{ablation-modality}
\label{modality-lost}
\begin{tabular}{c|ccccc}
    \toprule
    Modality & Acc7$\uparrow$  & Acc2$\uparrow$ & F1$\uparrow$ & MAE$\downarrow$ & Corr$\uparrow$ \\
    \midrule
    V+A & 42.0  &  59.4 / 62.1  &  61.2 / 62.6  & 0.808  & 0.228 \\
    L+A & 53.1  &  \underline{80.1} / 84.8  &  \underline{80.7} / 84.8  & 0.553  & 0.755 \\
    L+V & \underline{54.7} &  79.9 / \underline{85.1}  &  80.6 / \underline{85.1}  & \underline{0.547}  & \underline{0.762} \\
    \midrule
    HFR-AME & \textbf{55.0}  &  \textbf{84.5} / \textbf{86.3}  &  \textbf{84.6} / \textbf{86.2}  & \textbf{0.529}  &  \textbf{0.769} \\
    \bottomrule    
\end{tabular}
\end{table} 


\begin{table}[htbp]
\centering
% \caption{The Acc7 under different Missing Rates.}
\caption{Performance of our HFR-AME under various missing rates and hyper-parameters configurations on CMU-MOSEI dataset. $^{*}$ labels the default hyper-parameter values.}
\label{tab:parameter robustnes}
\setlength{\tabcolsep}{4pt}
\begin{tabular}{c|c|cccccc|c}
\toprule
% \multicolumn{9}{c}{{The ACC with different Missing Rates}} \\
% \midrule
% Parameters & Value & $0\%$ & $20\%$ & $40\%$ & $60\%$ & $80\%$ & $100\%$ & Avg\\
\multirow{2}{*}{Parameter} & \multirow{2}{*}{Value} & \multicolumn{6}{c|}{Missing Rate} & \multirow{2}{*}{Avg} \\
 % & & $0\%$ & $20\%$ & $40\%$ & $60\%$ & $80\%$ & $100\%$ & \\
 & & $0\%$ & $10\%$ & $30\%$ & $50\%$ & $70\%$ & $90\%$ & \\
\midrule
\multirow{3}{*}{$T$}
    % & 1 & 83.1 & 82.2 & 77.5 & 74.9 & 65.8 & 38.6 & 70.4 \\
    & 2 & 83.5 & 83.8 & 81.2 & 80.5 & 80.1 & 57.0 & 77.7 \\
    & 3$^{*}$ & 86.4 & 85.6 & 84.9 & 84.4 & 82.8 & 79.9 & \textbf{84.0} \\
    & 5 & 85.3 & 85.4 & 84.6 & 85.1 & 84.0 & 63.8 & \underline{81.4} \\
\midrule
\multirow{3}{*}{$\alpha$}
    % & 0.2 & 85.6 & 84.5  & 84.2 & 83.2 & 71.5 & 37.2 & 74.35 \\
    % & 0.4 & 86.4 & 85.1  & 84.3 & 83.4 & 82.1 & 62.9 & \textbf{80.69} \\
    % & 0.6 & 84.9 & 84.8  & 84.6 & 84.0 & 83.9 & 37.2 & 76.57 \\
    & 0.2 & 85.6 & 85.6 & 84.7 & 83.5 & 80.1 & 40.2 & 76.6 \\
    % & 0.3 & 85.7 & 85.5 & 85.1 & 84.4 & 84.1 & 71.4 & 82.70 \\
    & 0.4$^{*}$ & 86.4 & 85.6 & 84.9 & 84.4 & 82.8 & 79.9 & \underline{84.0} \\
    % & 0.6 & 84.9 & 85.0 & 84.9 & 84.6 & 83.9 & 83.9 & \textbf{84.53} \\
    & 0.7 & 85.4 & 85.0 & 84.7 & 84.7 & 84.4 & 80.3 & \textbf{84.1} \\

\midrule
\multirow{3}{*}{$\lambda$}
    & 0.01 & 85.0 & 83.5 & 79.6 & 76.2 & 67.3 & 39.6 & 71.9 \\
    & 0.1$^{*}$  & 86.4 & 85.6 & 84.9 & 84.4 & 82.8 & 79.9 & \textbf{84.0} \\
    & 0.5  & 85.5 & 85.3 & 85.5 & 85.0 & 83.5 & 61.8 & \underline{81.1} \\
\bottomrule
\end{tabular}
\end{table}

% \subsubsection{Hyper-parameter analysis of the weight $\lambda$}
% After analyzing the contribution of frequency feature restoration on the model, we conduct a sensitivity analysis to examine the impact of varying weights $\lambda$ in the loss function. We assessed its effect by assigning its value as 0.01, 0.05, 0.1, 0.3, and 0.5, and the corresponding performances are shown in  Fig. \ref{TTTaccuracy} (c).

%  Fig. \ref{TTTaccuracy} (c) shows that setting the value of $\lambda$ too small during model training results in insufficient emphasis on restoring the frequency of impaired modal features, thereby hindering the enhancement and generation of distinctive modal features. Conversely, setting $\lambda$ too large could not make a model focus on learning emotion-related semantic information, and thus fails to eliminate irrelevant redundant information, which negatively impacts the model’s ability in sentiment analysis. Consequently, a weight value of 0.1 was selected for the experimental section of this study.






\subsubsection{Robustness against Hyper-parameter Variations}
Toward the robustness under varying parameter settings, we conduct additional experiments and analyses, where diverse missing rates are used to evaluate this robustness, and the results are shown in Table~\ref{tab:parameter robustnes}.
 
 Table~\ref{tab:parameter robustnes} indicates that the performance of the model exhibits a progressive degradation as the missing rate escalates. Under mild to moderate missing rates, the performance decline is relatively slow, indicating that the model can effectively restore features and achieve modality alignment under these conditions. However, under severe missing rates, the performance drop becomes more pronounced. This is primarily attributed to the model's reduced ability in extracting complementary emotional information from other modalities, hindering the effective restoration of corrupted features. 
%
This sharp decline, particularly between 70\% and 90\% corruption, suggests that a critical information threshold may be reached, revealing a fundamental bottleneck in cross-modal complementarity where the source signal is  degraded too much to be effectively  restored.
%
Despite this, the model maintains robust performance across varying missing rates, demonstrating its resilience in scenarios with modal corruption.

The selection of iteration time $T$ plays a critical role in balancing the segmentation and restoration of frequency features. A moderate value of $T$ encourages the model to capture sufficient modal information while avoiding over-segmentation, which is particularly crucial for maintaining performance under high missing rates.


The margin $\alpha$, influences the model's ability in aligning and restoring features, by extracting complementary information from other modalities. A higher value of $\alpha$ enhances this capability, especially under severe missing conditions, by encouraging that the restored features are sufficiently distinct from the corrupted ones.


The weight $\lambda$ determines the trade-off between restoring impaired features and learning semantic information. Especially in scenarios with high missing rates, an optimal value of $\lambda$ encourages that the model neither neglects critical feature restoration nor overemphasizes irrelevant details when reconstructing impaired features.


In conclusion, Table~\ref{tab:parameter robustnes}  indicates that careful selection of $T$, $\alpha$, and $\lambda$ can largely enhance the model's robustness across varying missing rates. By optimizing these parameters, the HFR-AME model can maintain stable performance even in complex scenarios with severe data missingness.

% \subsubsection{Enhancing Fusion with DeBERTa}
\subsubsection{Robustness Analysis under Modality Corruption}



% To further validate the performance and robustness of our proposed fusion method, we conducted additional experiments using the DeBERTa model on the CMU-MOSEI dataset in Table \ref{tab:deberta}. 

To study the robustness of our model against the modality corruption, i.e. a core challenge to be addressed, we conducted a comparative study against several prominent fusion-centric models: Tensor Fusion Network (TFN) \cite{zadeh-etal-2017-tensor}, Multimodal Transformer (MulT) \cite{tsai2019multimodal}, and MultiModal InfoMax (MMIM) \cite{han-etal-2021-improving}. These models, while powerful, primarily focus on integrating complete data and are not explicitly designed to restore corrupted features. We simulated modality corruption by applying varying missing rates (from 0\% to 90\%) to the input features of all models on the CMU-MOSEI dataset. In this experiment, we also evaluated an enhanced version of our model using DeBERTa \cite{he2021deberta} as the text encoder, to investigate if a stronger feature extractor could further boost resilience. The results are shown in Table \ref{tab:deberta}.

As shown in Table \ref{tab:deberta}, our HFR-AME framework, even with a standard BERT encoder, consistently outperforms the baseline models across all corruption rates, achieving a higher average accuracy (\textbf{Avg}) and a smaller performance drop under 0\% to 90\% corruption (\textbf{Drop}). This highlights the critical advantage of our explicit restoration approach over traditional fusion-based methods. Furthermore, using the DeBERTa encoder yields an additional enhancement, improving the Acc2 metric by 1.0\% on average over the BERT version. This is primarily due to DeBERTa's ability in extracting richer semantic information, which provides a more robust foundation for our cross-modal restoration. 
%
% Notably, the most significant performance decline for all models occurs between 70\% and 90\% corruption. This suggests a critical information threshold has been reached, revealing a fundamental bottleneck in cross-modal complementarity where the source signal becomes too degraded to be meaningfully restored.
Notably, the most significant performance decline for all models occurs between 70\% and 90\% corruption. This suggests that a critical information threshold may be reached. We attribute this to a form of modality collapse \cite{chaudhuri2025closer}, where extreme noise from the corrupted modality overwhelms the fusion process. This reveals a fundamental bottleneck in cross-modal complementarity, as the source signal becomes too degraded to be meaningfully restored by any method. 
% The modality corruption was simulated at the feature level, as detailed and visualized in Section \ref{sec:visualizations}.




\begin{table}[htbp]
\centering
% \caption{Performance and robustness evaluation of HFR-AME using DeBERTa for textual features extraction on CMU-MOSEI dataset.}
\caption{Performance and robustness evaluation on CMU-MOSEI under various modality missing rates. Our restorative HFR-AME framework (with both BERT and DeBERTa encoders) is compared against prominent fusion-based baselines (TFN, MulT).}
\label{tab:deberta}
\setlength{\tabcolsep}{5pt}
\begin{tabular}{c|cccccc|c|c}
\toprule
\multirow{2}{*}{Model} & \multicolumn{6}{c|}{Missing Rate} & \multirow{2}{*}{Avg $\uparrow$ } & \multirow{2}{*}{\makecell{Drop \\ 0\% -> 90\%} $\downarrow$  } \\
 & $0\%$ & $10\%$ & $30\%$ & $50\%$ & $70\%$ & $90\%$ & & \\
\midrule
\text{TFN} & 82.6 & 80.8 & 80.6 & 79.4 & 78.2 & 68.8 & 78.4 & 13.8 \\
\text{Mult} & 84.0 & 80.4 & 77.2 & 75.8 & 70.6 & 65.4 & 75.6 & 18.6 \\
% \text{MISA} & 83.5 & 82.3 & 81.6 & 77.0 & 76.1 &  &  &  \\
\text{MMIM\textsubscript{BERT}} & 85.1 & 81.2 & 80.8 & 79.7 & 79.6 & 78.1 & 80.8 & 7.0 \\
\text{HFR-AME\textsubscript{BERT}} & \underline{86.4} & \underline{85.6} & \underline{84.9} & \underline{84.4} & \underline{82.8} & \underline{79.9} & \underline{84.0} & \underline{6.5} \\
\text{HFR-AME\textsubscript{DeBERTa}} & \textbf{87.9} & \textbf{87.7} & \textbf{87.8} & \textbf{87.6} & \textbf{87.1} & \textbf{82.0} & \textbf{85.0} & \textbf{5.9} \\
\bottomrule
\end{tabular}
\end{table}


% In scenarios with modality impairments, Table \ref{tab:deberta} demonstrates that our DeBERTa-based model maintains robust performance under mild to moderate impairments, effectively repairing the impaired modalities. Even in the presence of severe impairments, the model continues to perform well, indicating enhanced robustness. This is attributed to the more comprehensive semantic features extracted by the DeBERTa model, which enhance cross-modal semantic information interactions, and improve the model's ability in repairing corrupted modalities, during the reconstruction of these enriched informational features. These results underscore the effectiveness of our fusion method in handling modality impairments. 


\section{Computational Complexity Analysis} \label{sec:complexity_analysis}


To evaluate the practical applicability and computational cost of our HFR-AME framework, especially considering its iterative manner, we conducted a comprehensive complexity analysis. As shown in Table \ref{tab:complexity}, we compare the training time, inference time, and Floating-Point Operations (FLOPs) of our model with several representative baselines on the CMU-MOSEI dataset. For a fair comparison, all experiments were conducted on a single NVIDIA RTX 3090 GPU, and each method was run with its own default hyperparameter settings, including the batch size setting used in their original papers, to reflect its intended operational efficiency.

\begin{table}[h!] % [h!] is a placement specifier, adjust as needed (e.g., [t], [b])
\centering
\caption{Computational complexity analysis on the CMU-MOSEI dataset. Training time is measured per epoch. Inference time is averaged per sample.}
\label{tab:complexity}
\begin{tabular}{l c c c}
\toprule
\textbf{Method} & \textbf{Training Time} & \textbf{Inference Time} & \textbf{FLOPs} \\
& \textbf{(s/epoch)} & \textbf{(ms/sample)} & \textbf{(G)} \\
\midrule
% MulT       & 42.48 & 0.71 & 4.29 \\
MulT       & 88.22 & 1.71 & 0.03 \\
% MISA       & 55.54 & 0.93 & 4.26 \\
MISA       & 56.47 & 0.76 & 4.26 \\
% MissModal  & - & - & - \\
% MMIM  & 50.58 & 2.51 & 4.25 \\
MMIM  & 50.31 & 1.18 & 4.25 \\
EMT-DLFR+  & 99.60 & 4.63 & 8.64 \\
\midrule
\textbf{HFR-AME (Ours)} & 72.58 & 5.90 & 4.36 \\
\bottomrule
\end{tabular}
\end{table}


Table \ref{tab:complexity} indicates that HFR-AME achieves a competitive training efficiency, requiring less time per epoch than other complex models like MulT and EMT-DLFR+. In terms of FLOPs, our model's computational workload is comparable to that of MISA and MMIM, and significantly lower than EMT-DLFR+, demonstrating that our design does not introduce excessive computational overhead.
For the slightly higher inference time, this is resulted from the iterative frequency restoration process within the HFR module, where the process inherently involves multiple forward passes for enhancing robustness against corruption. 
%Specifically, this process, crucial for enhancing robustness against corruption, inherently involves multiple forward passes. %Overall, this analysis reveals a deliberate and favorable trade-off: for a modest and manageable increase in inference cost, our framework delivers substantial improvements in accuracy and robustness, confirming its practical value in real-world applications where data quality can be compromised.


\section{Visualizations} \label{sec:visualizations}


\subsection{Robustness against different corruptions}
% To further demonstrate the robustness of our model in resisting modality detail corruption, additional experiments are conducted to investigate scenarios where detailed information is corrupted at varying proportions. 
To further demonstrate the robustness of our model in resisting modality detail corruption, we conducted additional experiments investigating scenarios, where detailed information is corrupted at varying proportions, i.e. mild (10\%), moderate (50\%), and severe (90\%) impairments.
We utilize t-SNE \cite{van2008visualizing} to visually represent the original and learned vectors, as illustrated in Fig. \ref{fig:visualization}. In the pre-extracted features of three modalities, we applied a certain proportion of zeroing operations to each feature in the sequence, to simulate real-world scenarios of corrupted detailed information in videos.
%
To be precise, this corruption is simulated at the feature level. Specifically, for a given rate of $p\%$, we deterministically set the preceding  dimensions ($\text{floor}(\frac{p}{100} \cdot d)$) of the feature vector (of dimension $d$) to zero for every element in the sequence. This simulates a systematic feature degradation rather than a random loss of individual data points.


\begin{figure*}
    \centering
    \includegraphics[width=0.69\linewidth]{visualization_imgs/org_and_learned_feature.pdf}
    \caption{
    T-SNE visualization comparing the robustness of different methods against modality corruption on CMU-MOSEI. The first column shows the feature distribution of the original data. Subsequent columns represent increasing corruption rates (10\%, 50\%, and 90\%). Each row showcases the learned feature distributions from a different method: (a) Original corrupted vectors (no processing), (b) MulT \cite{tsai2019multimodal}, and (c) our HFR-AME. 
    % The colors red, blue, and green represent the negative ($<0$), neutral ($=0$), and positive ($>0$) classes, respectively.
    % t-SNE visualization of feature robustness under modality corruption on CMU-MOSEI. The first column shows original feature distributions; subsequent columns correspond to higher corruption rates (10\%, 50\%, 90\%). Each row represents: (a) corrupted input, (b) MulT \cite{tsai2019multimodal}, (c) our HFR-AME. The colors red, blue, and green represent the negative ($<0$), neutral ($=0$), and positive ($>0$) classes, respectively.
    }
    \label{fig:visualization}
\end{figure*}

As shown in Fig. \ref{fig:visualization}, which provides a  comparison among our HFR-AME, a prominent fusion-based baseline (MulT \cite{tsai2019multimodal}), and the original corrupted features across different corruption levels. We observe that under mild and moderate corruption, the learned feature vectors maintain clear and distinct categorical boundaries despite increased disorder in the original feature distribution, indicating the effectiveness of our model in recovering meaningful information from degraded inputs. 
%
In contrast, the MulT baseline exhibits more overlap and less robust clustering as corruption intensifies, indicating its relative vulnerability to feature degradation.
%
In cases of severe corruptions, where original features are highly compromised, the HFR-AME framework continues to produce discernible clusters by leveraging cross-modal guidance for robust restoration, albeit potentially leading to a decline in classification accuracy. This sustained performance is attributed to the ability of our model in utilizing complementary cues from other modalities, facilitating the restoration of degraded data and enhancing overall feature integrity.

\begin{figure*}
    \centering
\includegraphics[width=0.95\linewidth]{visualization_imgs/TextAudioVideo.pdf}
    %  Original frames from the video and the 
    \caption{Visualization of the original data (1st column) and learned data (2nd column) across text, audio, and vision modalities under different missing rates. The colors red, blue, and green represent the negative ($<0$), neutral ($=0$), and positive ($>0$) classes, respectively.}
    \label{fig:modality_table}
\end{figure*}

\subsection{Impact of HFR-AME for corrupted details}
To further investigate the resisting-corruption impact of our HFR-AME on multimodal representation with corrupted details, we apply varying proportions of missing masks to the three modalities, and then compare the original fusion vectors with those learned through our HFR-AME model, in Fig. \ref{fig:modality_table}. 

As shown in Fig. \ref{fig:modality_table}, despite details from each modality being masked, the HFR-AME model can still cluster the learned vectors into distinct categories and form apparent boundary lines, showcasing its robustness in learning discriminative features across modalities.
(i) When masking operations are applied to the text modality, the significant information loss leads to less distinct boundaries between sentiment classes. These findings suggest that complete restoration remains challenging when text modality features are severely lacking, although our network is capable of effectively restoring missing textual cues. (ii) Regarding the audio modality, the absence of detailed cues results in noticeable scattering of class points in the original, unlearned features, underscoring the vulnerability of audio data. (iii) By contrast, when vision information is corrupted, the learned features are consistently divided into distinct clusters, highlighting the stability of our model.

Moreover, when the text modality is compromised, the feature distributions across different categories undergo the most significant changes, making it difficult to establish clear category boundaries. In contrast, the corruption of either audio or vision modality features results in disordered original feature distributions. However, after processing via the trained HFR-AME network, features from different categories are still grouped into distinct clusters. These findings not only validate the robustness of our model, but also justify the textual modality as the most critical feature among various modalities.


\subsection{Contributions of different frequency bands}
Furthermore, we shed light on our AME module, to study whether it can focus on frequency features that are beneficial to sentiment analysis. Fig. \ref{mfrequency} shows the different frequency bands of the video frames, where `weight' represents the proportion of contribution of each frequency band on sentiment analysis. 

As shown in Fig. \ref{mfrequency}, (i) the low-frequency band primarily captures the overall contours of the image and large-scale lighting variations. While these cues are important for scene understanding, its role in emotion expression is relatively limited. 
(ii) In contrast, the mid-frequency and high-frequency bands contain key features of expressions, such as the contours and details of facial features. Due to the higher signal-to-noise ratio and relatively less background interference
in the mid-frequency and high-frequency bands, these bands are able to convey more effective emotion-related cues. 

Given that different frequency bands contain varying types of feature information, we applied fine-grained restoration and enhancement to each band in the HFR module, and performed adaptive fusion in the AME module. These results in Fig. \ref{mfrequency} further demonstrate that the model tends to emphasize features from frequency bands strongly associating with emotion analysis (mid-frequency and high-frequency bands, weight $>$ 0.4), while suppressing features from less relevant bands (low-frequency band, weight $<$ 0.2).

\begin{figure*}
    \centering
\includegraphics[width=0.9\linewidth]{visualization_imgs/MFrequency.pdf}
    %  Original frames from the video and the 
    \caption{Visualization of the original video frames (first row) and their corresponding high-frequency (second row), mid-frequency (third row), low-frequency (fourth row) bands, along with the weighted components of different frequency bands.}
    \label{mfrequency}
\end{figure*}





\section{CONCLUSION} \label{sec:conclusion}

For the task of Multimodal Sentiment Analysis (MSA), current algorithms rarely consider the long-standing challenge of modal detail corruption, possibly due to the overmuch missing of crucial emotional semantics in modal representation.
To address this issue, we proposed to resist corruption via the introduced HFR-AME framework to restore corrupted detail cues in video sequences, aiming at enhancing the efficiency and robustness of sentiment analysis from a frequency domain perspective. Specifically, to repair tainted detailed information from low-frequency to high-frequency bands,
we introduce the HFR module to leverage two complementary modal features as conditions. Concurrently, to enhance semantically relevant frequency bands, and enforce the restoration of modality features for discriminative representations, we propose the AME module with adaptively integrating diverse frequency features.
% Extensive experimental results on two commonly used datasets reveal the superiority of our approach over the state of the arts (SOTAs), in terms of most of quantitative metrics. 
%
Our HFR-AME framework establishes state-of-the-art results on the CMU-MOSI and CMU-MOSEI datasets, improving 7-class accuracy by 0.5\% and 0.6\%, respectively. Further analysis validates its robustness in cross-lingual settings and highlights its competitive computational efficiency. Supported by extensive ablation  analyses, it confirms that our frequency-based restoration approach is a promising step towards building more robust MSA systems.
%
%We conduct extensive 
Furthermore, 
% ablation studies and %illuminate the role of each module in our framework. 
visualization analysis shows the excellent restoration capabilities of our HFR-AME in the absence of detailed modal cues.

In future work, we will focus on tackling more complex real-world scenarios, specifically addressing situations where certain modality information is entirely missing due to recording device malfunctions. Additionally, while the current model performs competitively, it lacks sufficient efficiency and design unification across different modalities. Therefore, we plan to develop a more lightweight feature restoration module capable of handling multiple modalities, thereby reducing the storage overhead while improving restoration capabilities.


\begin{acks}
The authors would like to thank the anonymous reviewers for their constructive suggestions.
The work was supported by the Natural Science Foundation of China under grants no. 62276170, 82261138629, 62306061,
the Science and Technology Project of Guangdong Province under grant no. 2023A1515010688,
 the Science and Technology Innovation Commission of Shenzhen under grant no. JCYJ20220531101412030, the Open Research Fund from Guangdong Laboratory of Artificial Intelligence and Digital Economy (SZ) under Grant No. GML-KF-24-11, Guangdong Provincial Key Laboratory under grant no. 2023B1212060076.
\end{acks}

%%
%% The next two lines define the bibliography style to be used, and
%% the bibliography file.
\bibliographystyle{ACM-Reference-Format}
\bibliography{sample-base}

% ===== 在这里添加补充材料 =====
\clearpage
% \begin{appendix}

\input{supp}

% \end{appendix}
% =============================

\end{document}
\endinput
%%
%% End of file `sample-manuscript.tex'.
