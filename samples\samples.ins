\def\batchfile{samples.ins}
\input docstrip
\keepsilent
\showprogress


\askforoverwritefalse

\generate{%
  \file{sample-manuscript.tex}{\from{samples.dtx}{all,proceedings,bibtex,manuscript}}
  \file{sample-acmsmall.tex}{\from{samples.dtx}{all,journal,bibtex,acmsmall}}
  \file{sample-acmsmall-submission.tex}{\from{samples.dtx}{all,journal,bibtex,acmsmall-submission}}
  \file{sample-acmsmall-biblatex.tex}{\from{samples.dtx}{all,journal,acmsmall-biblatex}}
  \file{sample-acmlarge.tex}{\from{samples.dtx}{all,journal,bibtex,acmlarge}}
  \file{sample-acmtog.tex}{\from{samples.dtx}{all,journal,bibtex,acmtog}}
  \file{sample-sigconf.tex}{\from{samples.dtx}{all,proceedings,bibtex,sigconf}}
  \file{sample-sigconf-biblatex.tex}{\from{samples.dtx}{all,proceedings,sigconf-biblatex}}
  \file{sample-sigconf-authordraft.tex}{\from{samples.dtx}{all,proceedings,bibtex,authordraft}}
  \file{sample-sigconf-i13n.tex}{\from{samples.dtx}{all,proceedings,bibtex,sigconf-i13n}}
  \file{sample-sigconf-xelatex.tex}{\from{samples.dtx}{all,proceedings,bibtex,sigconf}}
  \file{sample-sigconf-lualatex.tex}{\from{samples.dtx}{all,proceedings,bibtex,sigconf}}     
  \file{sample-sigplan.tex}{\from{samples.dtx}{all,proceedings,bibtex,sigplan}}
  \file{sample-acmsmall-conf.tex}{\from{samples.dtx}{all,proceedings,bibtex,acmsmall-conf}}
  \file{sample-acmtog-conf.tex}{\from{samples.dtx}{all,journal,proceedings,bibtex,acmtog-conf}}
  \file{sample-acmcp.tex}{\from{samples.dtx}{all,journal,acmcp}}          
  \file{sample-acmengage.tex}{\from{acmengage.dtx}{acmengage}}
  \file{sample-acmsmall-tagged.tex}{\from{samples.dtx}{all,journal,acmsmall,tagged}}
}

